/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "GeosAdapter.h"
#include "Log.h"

#ifdef ROCKY_HAS_GEOS
#include <geos_c.h>
#endif

#include <cstdarg>
#include <cmath>
#include <cfloat>

using namespace ROCKY_NAMESPACE;

GeosAdapter::GeosAdapter() :
#ifdef ROCKY_HAS_GEOS
                             _context(nullptr),
                             _wktReader(nullptr),
                             _wktWriter(nullptr),
#endif
                             _initialized(false)
{
}

GeosAdapter::~GeosAdapter()
{
    finalize();
}

bool GeosAdapter::initialize()
{
#ifdef ROCKY_HAS_GEOS
    if (_initialized)
        return true;

    // 初始化GEOS库
    _context = GEOS_init_r();
    if (!_context)
    {
        Log()->error("Failed to initialize GEOS context");
        return false;
    }

    // 设置错误和警告处理器
    GEOSContext_setErrorHandler_r(_context, errorHandler);
    GEOSContext_setNoticeHandler_r(_context, warningHandler);

    // 创建WKT读写器
    _wktReader = GEOSWKTReader_create_r(_context);
    _wktWriter = GEOSWKTWriter_create_r(_context);

    if (!_wktReader || !_wktWriter)
    {
        Log()->error("Failed to create GEOS WKT readers/writers");
        finalize();
        return false;
    }

    _initialized = true;
    Log()->info("GEOS adapter initialized successfully");
    return true;
#else
    Log()->error("GEOS support not compiled in");
    return false;
#endif
}

void GeosAdapter::finalize()
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized)
        return;

    if (_wktWriter)
    {
        GEOSWKTWriter_destroy_r(_context, _wktWriter);
        _wktWriter = nullptr;
    }

    if (_wktReader)
    {
        GEOSWKTReader_destroy_r(_context, _wktReader);
        _wktReader = nullptr;
    }

    if (_context)
    {
        GEOS_finish_r(_context);
        _context = nullptr;
    }

    _initialized = false;
    Log()->info("GEOS adapter finalized");
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createPoint(double x, double y, double z)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized)
        return nullptr;

    GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, 1, 3);
    GEOSCoordSeq_setX_r(_context, coords, 0, x);
    GEOSCoordSeq_setY_r(_context, coords, 0, y);
    GEOSCoordSeq_setZ_r(_context, coords, 0, z);

    GEOSGeometry *geosPoint = GEOSGeom_createPoint_r(_context, coords);
    auto result = convertFromGEOS(geosPoint);
    GEOSGeom_destroy_r(_context, geosPoint);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createLineString(const std::vector<glm::dvec3> &points)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || points.empty())
        return nullptr;

    GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, points.size(), 3);
    for (size_t i = 0; i < points.size(); ++i)
    {
        GEOSCoordSeq_setX_r(_context, coords, i, points[i].x);
        GEOSCoordSeq_setY_r(_context, coords, i, points[i].y);
        GEOSCoordSeq_setZ_r(_context, coords, i, points[i].z);
    }

    GEOSGeometry *geosLine = GEOSGeom_createLineString_r(_context, coords);
    auto result = convertFromGEOS(geosLine);
    GEOSGeom_destroy_r(_context, geosLine);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createPolygon(const std::vector<glm::dvec3> &exteriorRing,
                                                     const std::vector<std::vector<glm::dvec3>> &holes)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || exteriorRing.size() < 3)
        return nullptr;

    // 创建外环
    GEOSCoordSequence *extCoords = GEOSCoordSeq_create_r(_context, exteriorRing.size(), 3);
    for (size_t i = 0; i < exteriorRing.size(); ++i)
    {
        GEOSCoordSeq_setX_r(_context, extCoords, i, exteriorRing[i].x);
        GEOSCoordSeq_setY_r(_context, extCoords, i, exteriorRing[i].y);
        GEOSCoordSeq_setZ_r(_context, extCoords, i, exteriorRing[i].z);
    }
    GEOSGeometry *shell = GEOSGeom_createLinearRing_r(_context, extCoords);

    // 创建内环
    GEOSGeometry **holeGeoms = nullptr;
    int numHoles = holes.size();
    if (numHoles > 0)
    {
        holeGeoms = new GEOSGeometry *[numHoles];
        for (int h = 0; h < numHoles; ++h)
        {
            const auto &hole = holes[h];
            if (hole.size() >= 3)
            {
                GEOSCoordSequence *holeCoords = GEOSCoordSeq_create_r(_context, hole.size(), 3);
                for (size_t i = 0; i < hole.size(); ++i)
                {
                    GEOSCoordSeq_setX_r(_context, holeCoords, i, hole[i].x);
                    GEOSCoordSeq_setY_r(_context, holeCoords, i, hole[i].y);
                    GEOSCoordSeq_setZ_r(_context, holeCoords, i, hole[i].z);
                }
                holeGeoms[h] = GEOSGeom_createLinearRing_r(_context, holeCoords);
            }
        }
    }

    GEOSGeometry *geosPolygon = GEOSGeom_createPolygon_r(_context, shell, holeGeoms, numHoles);

    if (holeGeoms)
        delete[] holeGeoms;

    auto result = convertFromGEOS(geosPolygon);
    GEOSGeom_destroy_r(_context, geosPolygon);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createMultiPoint(const std::vector<glm::dvec3> &points)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || points.empty())
        return nullptr;

    GEOSGeometry **pointGeoms = new GEOSGeometry *[points.size()];
    for (size_t i = 0; i < points.size(); ++i)
    {
        GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, 1, 3);
        GEOSCoordSeq_setX_r(_context, coords, 0, points[i].x);
        GEOSCoordSeq_setY_r(_context, coords, 0, points[i].y);
        GEOSCoordSeq_setZ_r(_context, coords, 0, points[i].z);
        pointGeoms[i] = GEOSGeom_createPoint_r(_context, coords);
    }

    GEOSGeometry *geosMultiPoint = GEOSGeom_createCollection_r(_context, GEOS_MULTIPOINT, pointGeoms, points.size());
    auto result = convertFromGEOS(geosMultiPoint);

    // 清理
    for (size_t i = 0; i < points.size(); ++i)
    {
        GEOSGeom_destroy_r(_context, pointGeoms[i]);
    }
    delete[] pointGeoms;
    GEOSGeom_destroy_r(_context, geosMultiPoint);

    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createMultiLineString(const std::vector<std::vector<glm::dvec3>> &lineStrings)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || lineStrings.empty())
        return nullptr;

    GEOSGeometry **lineGeoms = new GEOSGeometry *[lineStrings.size()];
    for (size_t i = 0; i < lineStrings.size(); ++i)
    {
        const auto &line = lineStrings[i];
        if (!line.empty())
        {
            GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, line.size(), 3);
            for (size_t j = 0; j < line.size(); ++j)
            {
                GEOSCoordSeq_setX_r(_context, coords, j, line[j].x);
                GEOSCoordSeq_setY_r(_context, coords, j, line[j].y);
                GEOSCoordSeq_setZ_r(_context, coords, j, line[j].z);
            }
            lineGeoms[i] = GEOSGeom_createLineString_r(_context, coords);
        }
    }

    GEOSGeometry *geosMultiLine = GEOSGeom_createCollection_r(_context, GEOS_MULTILINESTRING, lineGeoms, lineStrings.size());
    auto result = convertFromGEOS(geosMultiLine);

    // 清理
    for (size_t i = 0; i < lineStrings.size(); ++i)
    {
        GEOSGeom_destroy_r(_context, lineGeoms[i]);
    }
    delete[] lineGeoms;
    GEOSGeom_destroy_r(_context, geosMultiLine);

    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createMultiPolygon(const std::vector<std::shared_ptr<Geometry>> &polygons)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || polygons.empty())
        return nullptr;

    GEOSGeometry **polyGeoms = new GEOSGeometry *[polygons.size()];
    for (size_t i = 0; i < polygons.size(); ++i)
    {
        polyGeoms[i] = static_cast<GEOSGeometry *>(convertToGEOS(polygons[i].get()));
    }

    GEOSGeometry *geosMultiPoly = GEOSGeom_createCollection_r(_context, GEOS_MULTIPOLYGON, polyGeoms, polygons.size());
    auto result = convertFromGEOS(geosMultiPoly);

    // 清理
    for (size_t i = 0; i < polygons.size(); ++i)
    {
        GEOSGeom_destroy_r(_context, polyGeoms[i]);
    }
    delete[] polyGeoms;
    GEOSGeom_destroy_r(_context, geosMultiPoly);

    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createFromWKT(const std::string &wkt)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !_wktReader)
        return nullptr;

    GEOSGeometry *geosGeom = GEOSWKTReader_read_r(_context, _wktReader, wkt.c_str());
    auto result = convertFromGEOS(geosGeom);
    GEOSGeom_destroy_r(_context, geosGeom);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::buffer(const Geometry *geom, double distance)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return nullptr;

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return nullptr;

    GEOSGeometry *bufferedGeom = GEOSBuffer_r(_context, geosGeom, distance, 8);
    auto result = convertFromGEOS(bufferedGeom);

    GEOSGeom_destroy_r(_context, geosGeom);
    GEOSGeom_destroy_r(_context, bufferedGeom);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::intersection(const Geometry *geom1, const Geometry *geom2)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom1 || !geom2)
        return nullptr;

    GEOSGeometry *geos1 = static_cast<GEOSGeometry *>(convertToGEOS(geom1));
    GEOSGeometry *geos2 = static_cast<GEOSGeometry *>(convertToGEOS(geom2));

    if (!geos1 || !geos2)
    {
        if (geos1)
            GEOSGeom_destroy_r(_context, geos1);
        if (geos2)
            GEOSGeom_destroy_r(_context, geos2);
        return nullptr;
    }

    GEOSGeometry *intersectionGeom = GEOSIntersection_r(_context, geos1, geos2);
    auto result = convertFromGEOS(intersectionGeom);

    GEOSGeom_destroy_r(_context, geos1);
    GEOSGeom_destroy_r(_context, geos2);
    GEOSGeom_destroy_r(_context, intersectionGeom);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::unionGeometry(const Geometry *geom1, const Geometry *geom2)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom1 || !geom2)
        return nullptr;

    GEOSGeometry *geos1 = static_cast<GEOSGeometry *>(convertToGEOS(geom1));
    GEOSGeometry *geos2 = static_cast<GEOSGeometry *>(convertToGEOS(geom2));

    if (!geos1 || !geos2)
    {
        if (geos1)
            GEOSGeom_destroy_r(_context, geos1);
        if (geos2)
            GEOSGeom_destroy_r(_context, geos2);
        return nullptr;
    }

    GEOSGeometry *unionGeom = GEOSUnion_r(_context, geos1, geos2);
    auto result = convertFromGEOS(unionGeom);

    GEOSGeom_destroy_r(_context, geos1);
    GEOSGeom_destroy_r(_context, geos2);
    GEOSGeom_destroy_r(_context, unionGeom);
    return result;
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::difference(const Geometry *geom1, const Geometry *geom2)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom1 || !geom2)
        return nullptr;

    GEOSGeometry *geos1 = static_cast<GEOSGeometry *>(convertToGEOS(geom1));
    GEOSGeometry *geos2 = static_cast<GEOSGeometry *>(convertToGEOS(geom2));

    if (!geos1 || !geos2)
    {
        if (geos1)
            GEOSGeom_destroy_r(_context, geos1);
        if (geos2)
            GEOSGeom_destroy_r(_context, geos2);
        return nullptr;
    }

    GEOSGeometry *differenceGeom = GEOSDifference_r(_context, geos1, geos2);
    auto result = convertFromGEOS(differenceGeom);

    GEOSGeom_destroy_r(_context, geos1);
    GEOSGeom_destroy_r(_context, geos2);
    GEOSGeom_destroy_r(_context, differenceGeom);
    return result;
#else
    return nullptr;
#endif
}

bool GeosAdapter::intersects(const Geometry *geom1, const Geometry *geom2)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom1 || !geom2)
        return false;

    GEOSGeometry *geos1 = static_cast<GEOSGeometry *>(convertToGEOS(geom1));
    GEOSGeometry *geos2 = static_cast<GEOSGeometry *>(convertToGEOS(geom2));

    if (!geos1 || !geos2)
    {
        if (geos1)
            GEOSGeom_destroy_r(_context, geos1);
        if (geos2)
            GEOSGeom_destroy_r(_context, geos2);
        return false;
    }

    bool result = GEOSIntersects_r(_context, geos1, geos2) == 1;

    GEOSGeom_destroy_r(_context, geos1);
    GEOSGeom_destroy_r(_context, geos2);
    return result;
#else
    return false;
#endif
}

bool GeosAdapter::contains(const Geometry *geom1, const Geometry *geom2)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom1 || !geom2)
        return false;

    GEOSGeometry *geos1 = static_cast<GEOSGeometry *>(convertToGEOS(geom1));
    GEOSGeometry *geos2 = static_cast<GEOSGeometry *>(convertToGEOS(geom2));

    if (!geos1 || !geos2)
    {
        if (geos1)
            GEOSGeom_destroy_r(_context, geos1);
        if (geos2)
            GEOSGeom_destroy_r(_context, geos2);
        return false;
    }

    bool result = GEOSContains_r(_context, geos1, geos2) == 1;

    GEOSGeom_destroy_r(_context, geos1);
    GEOSGeom_destroy_r(_context, geos2);
    return result;
#else
    return false;
#endif
}

std::string GeosAdapter::toWKT(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !_wktWriter || !geom)
        return "";

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return "";

    char *wktStr = GEOSWKTWriter_write_r(_context, _wktWriter, geosGeom);
    std::string result(wktStr ? wktStr : "");

    if (wktStr)
        GEOSFree_r(_context, wktStr);
    GEOSGeom_destroy_r(_context, geosGeom);

    return result;
#else
    return "";
#endif
}

bool GeosAdapter::isValid(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return false;

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return false;

    bool valid = GEOSisValid_r(_context, geosGeom) == 1;
    GEOSGeom_destroy_r(_context, geosGeom);
    return valid;
#else
    return false;
#endif
}

double GeosAdapter::getArea(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return 0.0;

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return 0.0;

    double area = 0.0;
    GEOSArea_r(_context, geosGeom, &area);
    GEOSGeom_destroy_r(_context, geosGeom);
    return area;
#else
    return 0.0;
#endif
}

GeoExtent GeosAdapter::getBounds(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return GeoExtent();

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return GeoExtent();

    GEOSGeometry *envelope = GEOSEnvelope_r(_context, geosGeom);
    if (!envelope)
    {
        GEOSGeom_destroy_r(_context, geosGeom);
        return GeoExtent();
    }

    const GEOSGeometry *ring = GEOSGetExteriorRing_r(_context, envelope);
    if (!ring)
    {
        GEOSGeom_destroy_r(_context, geosGeom);
        GEOSGeom_destroy_r(_context, envelope);
        return GeoExtent();
    }

    const GEOSCoordSequence *coords = GEOSGeom_getCoordSeq_r(_context, ring);
    if (!coords)
    {
        GEOSGeom_destroy_r(_context, geosGeom);
        GEOSGeom_destroy_r(_context, envelope);
        return GeoExtent();
    }

    double minX = DBL_MAX, minY = DBL_MAX, maxX = -DBL_MAX, maxY = -DBL_MAX;
    unsigned int numPoints;
    GEOSCoordSeq_getSize_r(_context, coords, &numPoints);

    for (unsigned int i = 0; i < numPoints; ++i)
    {
        double x, y;
        GEOSCoordSeq_getX_r(_context, coords, i, &x);
        GEOSCoordSeq_getY_r(_context, coords, i, &y);

        minX = std::min(minX, x);
        minY = std::min(minY, y);
        maxX = std::max(maxX, x);
        maxY = std::max(maxY, y);
    }

    GEOSGeom_destroy_r(_context, geosGeom);
    GEOSGeom_destroy_r(_context, envelope);

    return GeoExtent(SRS::WGS84, minX, minY, maxX, maxY);
#else
    return GeoExtent();
#endif
}

double GeosAdapter::getLength(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return 0.0;

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return 0.0;

    double length = 0.0;
    GEOSLength_r(_context, geosGeom, &length);
    GEOSGeom_destroy_r(_context, geosGeom);
    return length;
#else
    return 0.0;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::simplify(const Geometry *geom, double tolerance)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return nullptr;

    GEOSGeometry *geosGeom = static_cast<GEOSGeometry *>(convertToGEOS(geom));
    if (!geosGeom)
        return nullptr;

    GEOSGeometry *simplifiedGeom = GEOSSimplify_r(_context, geosGeom, tolerance);
    auto result = convertFromGEOS(simplifiedGeom);

    GEOSGeom_destroy_r(_context, geosGeom);
    GEOSGeom_destroy_r(_context, simplifiedGeom);
    return result;
#else
    return nullptr;
#endif
}

GeosAdapter &GeosAdapter::instance()
{
    static GeosAdapter s_instance;
    return s_instance;
}

std::shared_ptr<Geometry> GeosAdapter::convertFromGEOS(void *geosGeom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geosGeom)
        return nullptr;

    GEOSGeometry *geom = static_cast<GEOSGeometry *>(geosGeom);
    int geomType = GEOSGeomTypeId_r(_context, geom);

    auto result = std::make_shared<Geometry>();

    switch (geomType)
    {
    case GEOS_POINT:
    {
        result->type = Geometry::Type::Points;
        const GEOSCoordSequence *coords = GEOSGeom_getCoordSeq_r(_context, geom);
        if (coords)
        {
            double x, y, z = 0.0;
            GEOSCoordSeq_getX_r(_context, coords, 0, &x);
            GEOSCoordSeq_getY_r(_context, coords, 0, &y);
            GEOSCoordSeq_getZ_r(_context, coords, 0, &z);
            result->points.emplace_back(x, y, z);
        }
        break;
    }
    case GEOS_LINESTRING:
    {
        result->type = Geometry::Type::LineString;
        const GEOSCoordSequence *coords = GEOSGeom_getCoordSeq_r(_context, geom);
        if (coords)
        {
            unsigned int size;
            GEOSCoordSeq_getSize_r(_context, coords, &size);
            result->points.reserve(size);

            for (unsigned int i = 0; i < size; ++i)
            {
                double x, y, z = 0.0;
                GEOSCoordSeq_getX_r(_context, coords, i, &x);
                GEOSCoordSeq_getY_r(_context, coords, i, &y);
                GEOSCoordSeq_getZ_r(_context, coords, i, &z);
                result->points.emplace_back(x, y, z);
            }
        }
        break;
    }
    case GEOS_POLYGON:
    {
        result->type = Geometry::Type::Polygon;

        // 获取外环
        const GEOSGeometry *exteriorRing = GEOSGetExteriorRing_r(_context, geom);
        if (exteriorRing)
        {
            const GEOSCoordSequence *coords = GEOSGeom_getCoordSeq_r(_context, exteriorRing);
            if (coords)
            {
                unsigned int size;
                GEOSCoordSeq_getSize_r(_context, coords, &size);
                result->points.reserve(size);

                for (unsigned int i = 0; i < size; ++i)
                {
                    double x, y, z = 0.0;
                    GEOSCoordSeq_getX_r(_context, coords, i, &x);
                    GEOSCoordSeq_getY_r(_context, coords, i, &y);
                    GEOSCoordSeq_getZ_r(_context, coords, i, &z);
                    result->points.emplace_back(x, y, z);
                }
            }
        }

        // 获取内环
        int numHoles = GEOSGetNumInteriorRings_r(_context, geom);
        if (numHoles > 0)
        {
            result->parts.reserve(numHoles);
            for (int h = 0; h < numHoles; ++h)
            {
                const GEOSGeometry *hole = GEOSGetInteriorRingN_r(_context, geom, h);
                if (hole)
                {
                    Geometry holePart;
                    holePart.type = Geometry::Type::LineString;

                    const GEOSCoordSequence *holeCoords = GEOSGeom_getCoordSeq_r(_context, hole);
                    if (holeCoords)
                    {
                        unsigned int holeSize;
                        GEOSCoordSeq_getSize_r(_context, holeCoords, &holeSize);
                        holePart.points.reserve(holeSize);

                        for (unsigned int i = 0; i < holeSize; ++i)
                        {
                            double x, y, z = 0.0;
                            GEOSCoordSeq_getX_r(_context, holeCoords, i, &x);
                            GEOSCoordSeq_getY_r(_context, holeCoords, i, &y);
                            GEOSCoordSeq_getZ_r(_context, holeCoords, i, &z);
                            holePart.points.emplace_back(x, y, z);
                        }
                    }
                    result->parts.push_back(holePart);
                }
            }
        }
        break;
    }
    case GEOS_MULTIPOINT:
    {
        result->type = Geometry::Type::MultiPoints;
        int numGeoms = GEOSGetNumGeometries_r(_context, geom);
        result->parts.reserve(numGeoms);

        for (int i = 0; i < numGeoms; ++i)
        {
            const GEOSGeometry *subGeom = GEOSGetGeometryN_r(_context, geom, i);
            if (subGeom)
            {
                Geometry point;
                point.type = Geometry::Type::Points;
                const GEOSCoordSequence *coords = GEOSGeom_getCoordSeq_r(_context, subGeom);
                if (coords)
                {
                    double x, y, z = 0.0;
                    GEOSCoordSeq_getX_r(_context, coords, 0, &x);
                    GEOSCoordSeq_getY_r(_context, coords, 0, &y);
                    GEOSCoordSeq_getZ_r(_context, coords, 0, &z);
                    point.points.emplace_back(x, y, z);
                }
                result->parts.push_back(point);
            }
        }
        break;
    }
    case GEOS_MULTILINESTRING:
    {
        result->type = Geometry::Type::MultiLineString;
        int numGeoms = GEOSGetNumGeometries_r(_context, geom);
        result->parts.reserve(numGeoms);

        for (int i = 0; i < numGeoms; ++i)
        {
            const GEOSGeometry *subGeom = GEOSGetGeometryN_r(_context, geom, i);
            if (subGeom)
            {
                auto lineGeom = convertFromGEOS(const_cast<GEOSGeometry *>(subGeom));
                if (lineGeom)
                {
                    result->parts.push_back(*lineGeom);
                }
            }
        }
        break;
    }
    case GEOS_MULTIPOLYGON:
    {
        result->type = Geometry::Type::MultiPolygon;
        int numGeoms = GEOSGetNumGeometries_r(_context, geom);
        result->parts.reserve(numGeoms);

        for (int i = 0; i < numGeoms; ++i)
        {
            const GEOSGeometry *subGeom = GEOSGetGeometryN_r(_context, geom, i);
            if (subGeom)
            {
                auto polyGeom = convertFromGEOS(const_cast<GEOSGeometry *>(subGeom));
                if (polyGeom)
                {
                    result->parts.push_back(*polyGeom);
                }
            }
        }
        break;
    }
    default:
        Log()->warn("Unsupported GEOS geometry type: {}", geomType);
        return nullptr;
    }

    return result;
#else
    return nullptr;
#endif
}

void *GeosAdapter::convertToGEOS(const Geometry *geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return nullptr;

    switch (geom->type)
    {
    case Geometry::Type::Points:
    {
        if (geom->points.empty())
            return nullptr;

        GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, 1, 3);
        const auto &point = geom->points[0];
        GEOSCoordSeq_setX_r(_context, coords, 0, point.x);
        GEOSCoordSeq_setY_r(_context, coords, 0, point.y);
        GEOSCoordSeq_setZ_r(_context, coords, 0, point.z);
        return GEOSGeom_createPoint_r(_context, coords);
    }
    case Geometry::Type::LineString:
    {
        if (geom->points.empty())
            return nullptr;

        GEOSCoordSequence *coords = GEOSCoordSeq_create_r(_context, geom->points.size(), 3);
        for (size_t i = 0; i < geom->points.size(); ++i)
        {
            const auto &point = geom->points[i];
            GEOSCoordSeq_setX_r(_context, coords, i, point.x);
            GEOSCoordSeq_setY_r(_context, coords, i, point.y);
            GEOSCoordSeq_setZ_r(_context, coords, i, point.z);
        }
        return GEOSGeom_createLineString_r(_context, coords);
    }
    case Geometry::Type::Polygon:
    {
        if (geom->points.empty())
            return nullptr;

        // 创建外环
        GEOSCoordSequence *extCoords = GEOSCoordSeq_create_r(_context, geom->points.size(), 3);
        for (size_t i = 0; i < geom->points.size(); ++i)
        {
            const auto &point = geom->points[i];
            GEOSCoordSeq_setX_r(_context, extCoords, i, point.x);
            GEOSCoordSeq_setY_r(_context, extCoords, i, point.y);
            GEOSCoordSeq_setZ_r(_context, extCoords, i, point.z);
        }
        GEOSGeometry *shell = GEOSGeom_createLinearRing_r(_context, extCoords);

        // 创建内环
        GEOSGeometry **holeGeoms = nullptr;
        int numHoles = geom->parts.size();
        if (numHoles > 0)
        {
            holeGeoms = new GEOSGeometry *[numHoles];
            for (int h = 0; h < numHoles; ++h)
            {
                const auto &hole = geom->parts[h];
                if (!hole.points.empty())
                {
                    GEOSCoordSequence *holeCoords = GEOSCoordSeq_create_r(_context, hole.points.size(), 3);
                    for (size_t i = 0; i < hole.points.size(); ++i)
                    {
                        const auto &point = hole.points[i];
                        GEOSCoordSeq_setX_r(_context, holeCoords, i, point.x);
                        GEOSCoordSeq_setY_r(_context, holeCoords, i, point.y);
                        GEOSCoordSeq_setZ_r(_context, holeCoords, i, point.z);
                    }
                    holeGeoms[h] = GEOSGeom_createLinearRing_r(_context, holeCoords);
                }
            }
        }

        auto result = GEOSGeom_createPolygon_r(_context, shell, holeGeoms, numHoles);

        if (holeGeoms)
            delete[] holeGeoms;

        return result;
    }
    case Geometry::Type::MultiPoints:
    {
        if (geom->parts.empty())
            return nullptr;

        GEOSGeometry **pointGeoms = new GEOSGeometry *[geom->parts.size()];
        for (size_t i = 0; i < geom->parts.size(); ++i)
        {
            pointGeoms[i] = static_cast<GEOSGeometry *>(convertToGEOS(&geom->parts[i]));
        }

        auto result = GEOSGeom_createCollection_r(_context, GEOS_MULTIPOINT, pointGeoms, geom->parts.size());
        delete[] pointGeoms;
        return result;
    }
    case Geometry::Type::MultiLineString:
    {
        if (geom->parts.empty())
            return nullptr;

        GEOSGeometry **lineGeoms = new GEOSGeometry *[geom->parts.size()];
        for (size_t i = 0; i < geom->parts.size(); ++i)
        {
            lineGeoms[i] = static_cast<GEOSGeometry *>(convertToGEOS(&geom->parts[i]));
        }

        auto result = GEOSGeom_createCollection_r(_context, GEOS_MULTILINESTRING, lineGeoms, geom->parts.size());
        delete[] lineGeoms;
        return result;
    }
    case Geometry::Type::MultiPolygon:
    {
        if (geom->parts.empty())
            return nullptr;

        GEOSGeometry **polyGeoms = new GEOSGeometry *[geom->parts.size()];
        for (size_t i = 0; i < geom->parts.size(); ++i)
        {
            polyGeoms[i] = static_cast<GEOSGeometry *>(convertToGEOS(&geom->parts[i]));
        }

        auto result = GEOSGeom_createCollection_r(_context, GEOS_MULTIPOLYGON, polyGeoms, geom->parts.size());
        delete[] polyGeoms;
        return result;
    }
    default:
        Log()->warn("Unsupported Rocky geometry type: {}", static_cast<int>(geom->type));
        return nullptr;
    }
#else
    return nullptr;
#endif
}

void GeosAdapter::errorHandler(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);
    Log()->error("GEOS Error: {}", buffer);
}

void GeosAdapter::warningHandler(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);
    Log()->warn("GEOS Warning: {}", buffer);
}

// GeosGeometryFactory implementation
std::shared_ptr<Geometry> GeosGeometryFactory::createRectangle(double minX, double minY, double maxX, double maxY)
{
    std::vector<glm::dvec3> points = {
        {minX, minY, 0.0},
        {maxX, minY, 0.0},
        {maxX, maxY, 0.0},
        {minX, maxY, 0.0},
        {minX, minY, 0.0} // 闭合多边形
    };
    return GeosAdapter::instance().createPolygon(points);
}

std::shared_ptr<Geometry> GeosGeometryFactory::createCircle(double centerX, double centerY, double radius, int segments)
{
    std::vector<glm::dvec3> points;
    points.reserve(segments + 1);

    for (int i = 0; i <= segments; ++i)
    {
        double angle = 2.0 * M_PI * i / segments;
        double x = centerX + radius * std::cos(angle);
        double y = centerY + radius * std::sin(angle);
        points.emplace_back(x, y, 0.0);
    }

    return GeosAdapter::instance().createPolygon(points);
}

std::shared_ptr<Geometry> GeosGeometryFactory::createFromExtent(const GeoExtent &extent)
{
    return createRectangle(extent.west(), extent.south(), extent.east(), extent.north());
}
