﻿（一）构建CMake编译目录

忘掉历史处理，忘掉memory，重新分析项目代码。
1.更新库源代码
2.对本项目使用Qt5和Visual Studio 2012编译，C++语法使用C++17。
Qt5安装目录C:/Qt/Qt5.14.2/5.14.2/msvc2017_64，
python安装目录在C:\Python\Python313，其余按照Rule规定执行

3.为了编译成轻量的库，
打开 ROCKY_SUPPORTS_HTTPLIB， 打开 ROCKY_SUPPORTS_QT，打开 ROCKY_SUPPORTS_IMGUI，打开ROCKY_SUPPORTS_HTTPS, 
关闭 ROCKY_SUPPORTS_CURL，关闭ROCKY_SUPPORTS_GDAL， 关闭 ROCKY_SUPPORTS_MBTILES

使用CMake构建编译目录到F:/rockyb



ROCKY_RENDERER_VSG: 是否启用 VSG/Vulkan 渲染后端。
ROCKY_SUPPORTS_HTTPLIB: 是否通过 cpp-httplib 库支持 HTTP。
ROCKY_SUPPORTS_CURL: 是否通过 CURL 库支持 HTTP（与 HTTPLIB 互斥）。
ROCKY_SUPPORTS_HTTPS: 是否支持 HTTPS，此选项依赖 OpenSSL。
ROCKY_SUPPORTS_GDAL: 是否支持 GeoTIFF、WMS、WMTS 等格式，此选项依赖 GDAL。
ROCKY_SUPPORTS_MBTILES: 是否支持 MBTiles 数据库（需要 sqlite3 和 zlib）。
ROCKY_SUPPORTS_AZURE: 是否支持 Azure Maps。
ROCKY_SUPPORTS_BING: 是否支持 Bing Maps。
ROCKY_SUPPORTS_IMGUI: 是否支持 Dear ImGui 并构建相关示例。
ROCKY_SUPPORTS_QT: 是否支持 Qt 并构建相关示例。



检查并列出代码中各种可以配置的宏参数，为了编译成轻量的库，
关闭对GDAL、openssl的依赖，重新使用CMake构建
打开 ROCKY_SUPPORTS_HTTPLIB， 打开 ROCKY_SUPPORTS_QT， 
关闭 ROCKY_SUPPORTS_CURL，关闭ROCKY_SUPPORTS_HTTPS，修正Qt模块配置，重新CMake


存在大量 qtVsg 的问题，经检查是由于依赖库是Qt5的原因

已经安装到 redist_desk 目录，且使用C:\Qt\Qt6.3.1\6.7.2\msvc2019_64\bin\windeployqt6.exe
完成了qt依赖库的复制，但运行应用程序时，报告缺失动态库： 
vsgXchange.dll，vsg-14.dll ，fmt.dll，spdlog.dll，proj_9.dll，zlib1.dll，
以及proj库所需要的参数数据目录，接下来请检查并修正脚本，确保程序能够正确运行

cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake -DQt6_DIR=C:/Qt/Qt6.7.2/msvc2022_64/lib/cmake/Qt6 -DQT_PACKAGE_NAME=Qt6 && cmake --install . --prefix ../redist_desk



cmake --build . --config Release --target rocky_demo
cd .. && cmake --install build_desk --config Release --prefix redist_desk






//只适合用来看看
http://tile.opentopomap.org/1/0/0.png


//错误但支持离线下载
https://srtm.csi.cgiar.org/tile/tile.php?x={x}&y={y}&z={z}
https://srtm.csi.cgiar.org/tile/tile.php?x=0&y=0&z=1

png格式的高程
https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png

http://s3.amazonaws.com/elevation-tiles-prod/terrarium/1/0/0.png

tif格式的高程
https://readymap.org/readymap/tiles/1.0.0/116/

要付费
https://api.mapbox.com/v4/mapbox.terrain-rgb/1/0/0.png?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4M29iazA2Z2gycXA4N2pmbDZmangifQ.-g_vE53SD2WrJ6tFX7QHmA

csdn整理的数据源
https://blog.csdn.net/HelloEarth_/article/details/115718485

1.检查当前代码，瓦片cache功能的开启情况，目前运行软件后，cache目录没有保存瓦片；2.检查从高程瓦片计算像素点高度值的流程，应该将计算公式放在哪里合适，能够适应不同高程URL的读取，例如前面说的readymap和amazonaws，目前使用了amazonaws的高程tws服务，计算公式参考 https://docs.safe.com/fme/html/FME-Form-Documentation/FME-ReadersWriters/terraintilesaws/terraintilesaws.htm给出的信息：预计是 height = (red * 256 + green + blue / 256) - 32768。请根据以上信息修正代码，并编译发布


https://s3.amazonaws.com/elevation-tiles-prod/normal/8/213/111.png

127,188,234 -> (127*256+188+234/256)-32768 = -67.0859375
127,183,12  -> (127*256+183+12/256)-32768 = -72.9591

陆地：128,11,0 -> (128*256+11+0/256)-32768 = 11
      127,250,98 -> (127*256+250+98/256)-32768 = -5.61818
      
 经过手工计算， height = (red * 256 + green + blue / 256) - 32768 该公式应用于瓦片，得到的像素点位置 Height是合理的，但是在渲染画面中，高度值异常夸张，这在地球系统中（地球半径约6378公里），正确地形是比较缓和的；第二个问题是加了地形之后，瓦片之间会出现缝隙，这也可能是和高度的应用错误相关。因此，请排查代码中的高程应用于绘图的模式，解决高度绘制夸张的问题   
 



请仔细阅读代码找到数字地球瓦片拉取以后，输送给vsg后端渲染的接口，
并请给出如果要将绘图引擎迁移到osg，需要进行哪些开发准备，大约需要多少工作量