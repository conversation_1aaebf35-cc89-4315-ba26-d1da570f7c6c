# Rocky 渲染后端迁移分析：从 VSG 到 OSG

本文档旨在分析 `rocky` 数字地球项目的渲染流程，并为将渲染后端从 Vulkan Scene Graph (VSG) 迁移到 OpenSceneGraph (OSG) 提供技术指导和工作量评估。

### 1. 瓦片数据与VSG渲染后端的接口分析

`rocky` 项目中，瓦片数据的拉取和渲染是一个异步、多阶段的过程，它并非通过一个单一函数调用完成，而是由一个精巧的"分页器（Pager）"机制驱动。其核心在于将数据加载（IO密集型）与渲染对象创建（CPU/GPU密集型）分离。

关键接口和流程如下：

1.  **数据请求的发起**: `src/rocky/vsg/terrain/TerrainTilePager.cpp` 中的 `TerrainTilePager::ping()` 方法在每一帧遍历场景时被调用。它会检查哪些瓦片是可见的，并为需要加载数据或需要创建子瓦片的瓦片创建请求，将它们放入不同的队列中（`_loadData`, `_createChildren`等）。

2.  **异步数据加载**: `TerrainTilePager::update()` 方法处理这些队列。当一个瓦片需要数据时，它会调用 `requestLoadData()`。此函数的核心是：
    *   调用 `jobs::dispatch` 将一个加载任务分派到工作线程池。
    *   在工作线程中，调用 `TerrainTileModelFactory::createTileModel()`。这个工厂函数负责读取所有图层（影像、高程），并将它们合成为一个与渲染引擎无关的中间数据结构 `dataModel`。**这个 `dataModel` 包含了瓦片的原始像素数据和顶点几何数据，是数据端的最终产物。**

3.  **渲染资源的创建**: 数据加载完成后，`requestLoadData` 函数会调用 `engine->stateFactory.updateRenderModel()`。`stateFactory` 是 `TerrainStateFactory` 的实例，定义在 `src/rocky/vsg/terrain/TerrainState.cpp` 中。
    *   `TerrainStateFactory::updateRenderModel()` 方法是**数据与渲染引擎结合的关键接口**。它接收前面生成的 `dataModel`，然后创建所有VSG（Vulkan Scene Graph）后端所需的渲染对象，例如：
        *   `vsg::DescriptorImage` (用于纹理)
        *   `vsg::DescriptorBuffer` (用于UBO)
        *   `vsg::BindDescriptorSet` (将资源绑定到管线)
    *   这些VSG对象被打包成一个 `RenderModel` structure，并附加到对应的 `TerrainTileNode` 上。

4.  **渲染状态合并**: `requestMergeData()` 函数负责将新创建的VSG渲染资源正式应用到场景图中。它获取 `tile->renderModel.descriptors.bind` (一个 `vsg::BindDescriptorSet` 命令)，并将其添加到瓦片节点的 `vsg::StateGroup` 中。这样，在下一帧渲染时，Vulkan管线就会使用新的纹理和数据来绘制这个瓦片。

**接口总结**：瓦片数据输送给VSG后端的接口，可以理解为 `TerrainStateFactory` 类。它扮演了"适配器"的角色，将通用的 `TileModel` 数据结构转换为VSG专用的 `RenderModel`（即一系列Vulkan描述符和状态命令）。

### 2. 迁移到OSG的开发准备与工作量评估

将 `rocky` 的渲染后端从VSG迁移到OSG是一项重大的重构工作，但项目良好的分层设计（数据层与渲染层分离）为迁移提供了可能性。

#### **开发准备**

您需要创建一个与 `src/rocky/vsg/` 目录平行的 `src/rocky/osg/` 目录，并在其中实现OSG版本的渲染后端。主要工作包括：

1.  **创建OSG渲染状态工厂 (`OSGTerrainStateFactory`)**:
    *   这是迁移工作的核心。需要创建一个新类，它同样接收 `TileModel` 作为输入。
    *   其内部需要将 `TileModel` 中的图像数据转换为 `osg::Image` 和 `osg::Texture2D`。
    *   将几何数据转换为 `osg::Geometry`，包括 `osg::Vec3Array` (顶点), `osg::Vec2Array` (纹理坐标), 和 `osg::DrawElementsUInt` (索引)。
    *   将所有这些OSG对象组织到一个 `osg::StateSet` 中。

2.  **创建OSG瓦片节点 (`OSGTerrainTileNode`)**:
    *   创建一个继承自 `osg::Geode` 或 `osg::Group` 的新类。
    *   这个类将取代 `vsg::Group` 作为场景中单个瓦片的容器，并持有 `osg::Geometry`。

3.  **适配分页器逻辑 (`OSGTerrainTilePager`)**:
    *   `TerrainTilePager` 的核心调度逻辑（跟踪可见瓦片、管理LOD）可以复用。
    *   但与场景图交互的部分需要重写，例如 `requestCreateChildren()` 必须创建和添加 `OSGTerrainTileNode` 实例，`requestMergeData()` 则需要将 `osg::StateSet` 应用到对应的 `OSGTerrainTileNode` 上。

4.  **移植着色器 (Shaders)**:
    *   VSG使用的GLSL着色器位于 `src/rocky/vsg/shaders/`，它们是为Vulkan的管线编写的（例如使用UBO）。
    *   您需要将这些着色器移植为OSG (OpenGL) 兼容的GLSL。这通常意味着将UBOs改为传统的uniforms，并可能需要调整顶点属性的绑定方式。

5.  **重写主程序入口**:
    *   需要将 `rocky_demo` 或 `rocky_engine` 中基于VSG的查看器设置（`vsg::Viewer`, `vsg::Window`) 替换为OSG的等效实现 (`osgViewer::Viewer`, `osgGA::TrackballManipulator` 等)。

#### **工作量评估**

假设由一名同时熟悉 `rocky` 架构、OSG和现代C++的开发者来执行，工作量估算如下：

*   **阶段一：基础框架搭建 (3-5天)**
    *   创建新的 `rocky_osg_demo` 目标。
    *   初始化OSG查看器、窗口和事件处理器。
    *   建立基本的 `OSGTerrainTileNode` 类结构。

*   **阶段二：核心渲染逻辑实现 (7-12天)**
    *   实现 `OSGTerrainStateFactory`，完成 `TileModel` 到 `osg::Geometry` 和 `osg::Texture2D` 的完整转换。这是最复杂的部分。
    *   完成GLSL着色器的移植和调试。

*   **阶段三：分页与场景图逻辑集成 (5-8天)**
    *   修改 `TerrainTilePager` 以使其能操作OSG场景图。
    *   处理OSG的线程模型，确保在子线程中加载数据后能安全地更新主场景图。

*   **阶段四：集成、测试和优化 (5-10天)**
    *   将所有模块集成到一起。
    *   进行大量测试，修复渲染错误、崩溃和性能问题。
    *   优化纹理和几何体的创建/销毁流程。

**总计工作量预估**: **大约在 20 到 35 个人日之间**，即 **4 到 7 个完整的人周**。这个预估是比较理想的，实际开发中可能会遇到各种意想不到的问题，尤其是在调试和性能优化阶段。 