@echo off
echo 重新组织Rocky数据文件到bin目录...
echo.

:: 设置目录变量
set BIN_DIR=bin
set SOURCE_DIR=%~dp0

:: 确保bin目录存在
if not exist "%BIN_DIR%" (
    echo 错误: bin目录不存在
    pause
    exit /b 1
)

echo 步骤1: 复制字体文件...
if exist "%SOURCE_DIR%fonts" (
    if not exist "%BIN_DIR%\fonts" mkdir "%BIN_DIR%\fonts"
    xcopy "%SOURCE_DIR%fonts\*" "%BIN_DIR%\fonts\" /S /Y /Q
    echo 字体文件已复制到 %BIN_DIR%\fonts\
) else (
    echo 警告: fonts目录不存在
)

echo 步骤2: 复制着色器文件...
if exist "%SOURCE_DIR%shaders" (
    if not exist "%BIN_DIR%\shaders" mkdir "%BIN_DIR%\shaders"
    xcopy "%SOURCE_DIR%shaders\*" "%BIN_DIR%\shaders\" /S /Y /Q
    echo 着色器文件已复制到 %BIN_DIR%\shaders\
) else (
    echo 警告: shaders目录不存在
)

echo 步骤3: 复制数据文件...
if exist "%SOURCE_DIR%data" (
    if not exist "%BIN_DIR%\data" mkdir "%BIN_DIR%\data"
    xcopy "%SOURCE_DIR%data\*" "%BIN_DIR%\data\" /S /Y /Q
    echo 数据文件已复制到 %BIN_DIR%\data\
) else (
    echo 警告: data目录不存在
)

echo 步骤4: 复制share目录...
if exist "%SOURCE_DIR%share" (
    if not exist "%BIN_DIR%\share" mkdir "%BIN_DIR%\share"
    xcopy "%SOURCE_DIR%share\*" "%BIN_DIR%\share\" /S /Y /Q
    echo share文件已复制到 %BIN_DIR%\share\
) else (
    echo 警告: share目录不存在
)

echo 步骤5: 复制include文件（如果需要）...
if exist "%SOURCE_DIR%include" (
    if not exist "%BIN_DIR%\include" mkdir "%BIN_DIR%\include"
    xcopy "%SOURCE_DIR%include\*" "%BIN_DIR%\include\" /S /Y /Q
    echo include文件已复制到 %BIN_DIR%\include\
)

echo 步骤6: 复制库文件...
if exist "%SOURCE_DIR%lib" (
    if not exist "%BIN_DIR%\lib" mkdir "%BIN_DIR%\lib"
    xcopy "%SOURCE_DIR%lib\*" "%BIN_DIR%\lib\" /S /Y /Q
    echo 库文件已复制到 %BIN_DIR%\lib\
)

echo 步骤7: 创建新的启动脚本...
(
echo @echo off
echo :: Rocky Demo 启动脚本
echo set ROCKY_FILE_PATH=%%~dp0
echo set VSG_FILE_PATH=%%~dp0
echo set ROCKY_DEFAULT_FONT=%%~dp0fonts\calibri.ttf
echo.
echo echo 启动Rocky Demo...
echo echo 工作目录: %%CD%%
echo echo ROCKY_FILE_PATH=%%ROCKY_FILE_PATH%%
echo echo VSG_FILE_PATH=%%VSG_FILE_PATH%%
echo echo ROCKY_DEFAULT_FONT=%%ROCKY_DEFAULT_FONT%%
echo echo.
echo.
echo .\rocky_demo.exe --log-level info
echo pause
) > "%BIN_DIR%\start_rocky_demo.bat"

echo 新启动脚本已创建: %BIN_DIR%\start_rocky_demo.bat

echo.
echo 数据文件重新组织完成！
echo.
echo 现在可以使用以下启动脚本：
echo - %BIN_DIR%\start_rocky_demo.bat
echo.
echo 数据文件结构：
echo %BIN_DIR%\
echo   ├── fonts\         - 字体文件
echo   ├── shaders\       - 着色器文件  
echo   ├── data\          - 数据文件
echo   ├── share\         - 共享文件
echo   ├── include\       - 头文件（可选）
echo   └── lib\           - 库文件
echo.
pause 