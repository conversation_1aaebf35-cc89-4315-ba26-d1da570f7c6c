0.0 每次问题和回答，自动记录或者追加到 question.md 文件

0.0 从cursor发起的删除或者清理动作，必须先将文件删除到回收站，禁止直接删除源代码文件

0.0 代码强制使用UTF8字符编码，注意与操作系统API调用的编码转换问题，比如保存到磁盘文件时必须转到ANSI编码；

0.0 保存采用英文文件名优先；

0.0 本机python目录 c:\Python\Python313

0.1 项目中除了问答对话 和 markdow文档优先采用中文，其他一律优先使用英文，包括CMake，包括源代码

\0. 对话次数超限制后，请自动resume conversaton;

\0. 参考com技术，外部程序需要用到的类采用面向接口的编程方式，接口文件名后缀 _i.hpp；内部类采用正常类声明，接口类的派生实现类和内部类代码均放在库自己目录，不需要暴露和安装到sdk目录；

\1. 公用数据结构放到单独的datatype_.hpp，方便库和调用程序使用;

1.0 尽可能使用优秀的第三方开源库，例如yalantinglib, magic_enum, folly, tbb等，优化代码的质量；

1.1 只导出外部需要调用的受控库的函数，采用 C 语言方式，创建的对象作为函数返回参数，datatype_.h文件中的数据结构无需导出；

1.2  禁止使用base64编码相关的函数;

\2. 实现最大程度封装，对调用者只暴露接口头文件，必须隐藏具体实现类，实现类不需要.h头文件，全部放到.cpp文件（采用注册插件的方式应用，参考 C:\local\my_utilitis\my_plugin）。公用头文件和内部文件目录必须隔离，内部使用的头文件无需暴露给调用端;

\3. 有扩展性要求的子类体系，参考 C:\local\my_utilitis\my_plugin 实现的插件系统；

\4. 最大程度使用 C:\local\my_utilitis提供的基础封装，例如 exstring.hpp, expath.hpp，该目录由用户维护，AI无权限修改；

\5. CMake中规定C++使用20或者最新的语法标准；

\6. 使用中文回答问题和编写.md格式说明文档；

\7. 全英文编写代码，代码采用utf-8编码保存，在控制台添加utf8代码页，正确显示utf8字符；

\8. 源代码缩进采用空格，每个tab对应2字符宽度；

\9. 使用MS C++编译器的CMake默认需要添加/utf-8编译指令；

\10. 修复编译项目代码出现的错误和警告；

\11. 所有控制台信息都使用fmt库进行打印，区分日志等级，彩色输出；

\12. 本机vcpkg安装目录：C:\dev\vcpkg，头文件路径：C:\dev\vcpkg\installed\x64-windows\include；库文件路径：C:\dev\vcpkg\installed\x64-windows\lib;Debug版本库路径：C:\dev\vcpkg\installed\x64-windows\debug\lib;

12.1 本机Qt6安装目录 C:\Qt\Qt6.3.1\6.7.2，Qt5安装目录 C:\Qt\Qt5.14.2;

12.2 CMake编译临时目录放在 C:\VisualStudioRollback\CMakeBuild_Temp，发布目录在CMakeLists.txt文件同级的 redist_desk子目录；

\13. 为开发库编写开发帮助.md，要求描述开发库功能、特点、给出调用程序样例；

\14. 撰写技术架构设计.md，要求如下：“按照以下模板，撰写项目架构设计文档

要求：图文混编，图片使用mermaid，并保存为技术架构设计.md

\1. 项目概述

1.1 主要特点

1.2 技术栈

1.3 外部依赖

\2. 系统架构

2.1 整体架构图

2.2 数据流

\3. 主要组件详解

3.1 类图

3.2 LocalBlockStorage类

3.3 StorageSystemImpl类

3.4 Block类

\4. 关键数据结构

4.1 主要存储结构

4.2 内存数据结构

4.3 物理存储布局

\5. 关键算法和流程

5.1 文件写入流程

5.2 文件读取流程

5.3 索引压缩算法

5.4 块分配策略

\6. 并发控制

6.1 锁机制

6.2 线程模型

\7. 容错和恢复机制

7.1 索引保护

7.2 数据完整性

\8. 性能优化

8.1 针对HDD的优化

8.2 并发优化

8.3 内存优化

\9. 可扩展性考虑

\10. 限制和约束

\11. 未来改进方向

\12. 系统测试与性能评估

12.1 测试方法

12.2 基本功能测试

12.3 性能测试

12.4 目录导入测试

12.5 性能基准

12.6 测试工具辅助功能

12.7 测试结果分析”

\#################

\14. emscripten 编译器安装目录在 C:\dev\emsdk；

\15. webassembly 开发中，前端html/javascript(typescript)/css/vue等只负责GUI和鼠标、键盘消息的管理，软件逻辑全部放在C++端，webassembly C++模块不使用fmt库；

\16. 规定 webassembly项目，编译目录在项目文件夹下 build_wasm子目录，发布目录在 redist_wasm子目录；

\17. webassembly 要自动化完成编译和发布，编译过程禁止反复创建脚本，禁止新增模拟或者测试库来替代项目编译，禁止启动http服务；

\18. 规定 desktop项目，编译目录在项目文件夹下 build_desk子目录，发布目录在 redist_desk子目录，以后编译后需要将发布文件复制到 redist_desk子目录；

\19. c++ 开发webassembly, 与前端html/javascript(typescript)/css/vue等混合编程。前端负责GUI和鼠标、键盘消息的管理,文件数据的IO, 后端C++模块负责数据结构、业务逻辑；

\20. html/javascript非必要只用原生模块, 例如二维绘图canvas绘图即可;

\21. 封装前端的鼠标、键盘、触摸手势等交互消息, 通过单一接口handleEvent函数传递给C++, C++内部具体响应函数采用event_xxx命名规则;

\22. 封装所有的动作(包括获取信息),通过单一handleAction接口函数传递给C++, C++内部具体响应函数采用action_xxx命名规则;

\23. C++和javascript之间通过调用函数进行数据交换, 二进制数据采用线性内存区域交换(C++负责开辟), 简单对象采用json进行编解码,  解决参数数据传递;

\24. 为保证高效，图像像素数据采用线性内存传递(C++负责开辟), 禁用base64编码方式传递;