@echo off
setlocal enabledelayedexpansion

echo 编译Rocky项目...
set BUILD_DIR=F:/rockyb2
set SRC_DIR=%~dp0

if not exist "%BUILD_DIR%" (
    echo 错误: 编译目录 %BUILD_DIR% 不存在.
    echo 请先运行 build_config_core.bat.
    pause
    exit /b 1
)

echo 编译目录: %BUILD_DIR%
echo.

echo 开始编译 Release 版本...
cmake --build "%BUILD_DIR%" --config Release -j%NUMBER_OF_PROCESSORS%
if !errorlevel! neq 0 (
    echo.
    echo 编译失败!
    pause
    exit /b 1
)
echo 编译成功.
echo.

echo 复制文件到发布目录...
set REDIST_DIR=%SRC_DIR%redist_desk
set BIN_DIR=%REDIST_DIR%\bin
mkdir "%REDIST_DIR%" >nul 2>nul
mkdir "%BIN_DIR%" >nul 2>nul

:: 复制DLLs到bin目录
xcopy /y /q "%BUILD_DIR%\src\rocky\Release\*.dll" "%BIN_DIR%\"
echo DLLs 已复制到 bin\

:: 复制可执行文件到bin目录
xcopy /y /q "%BUILD_DIR%\src\apps\rocky_demo\Release\rocky_demo.exe" "%BIN_DIR%\"
xcopy /y /q "%BUILD_DIR%\src\apps\rocky_simple\Release\rocky_simple.exe" "%BIN_DIR%\"
xcopy /y /q "%BUILD_DIR%\src\apps\rocky_engine\Release\rocky_engine.exe" "%BIN_DIR%\"
xcopy /y /q "%BUILD_DIR%\src\tests\Release\rocky_tests.exe" "%BIN_DIR%\"
echo EXEs 已复制到 bin\

:: 复制库文件到根目录（用于开发）
xcopy /y /q "%BUILD_DIR%\src\rocky\Release\rocky.lib" "%REDIST_DIR%\"
xcopy /y /q "%BUILD_DIR%\src\rocky\Release\rocky.exp" "%REDIST_DIR%\"
echo LIB 已复制到根目录

:: 复制数据文件到bin目录内部
echo 复制着色器到bin目录...
xcopy /s /y /q "%SRC_DIR%src\rocky\vsg\shaders" "%BIN_DIR%\shaders\"
echo 着色器已复制到 bin\shaders\

echo 复制字体到bin目录...
xcopy /s /y /q "%SRC_DIR%data\fonts" "%BIN_DIR%\fonts\"
echo 字体已复制到 bin\fonts\

echo 复制数据文件到bin目录...
if exist "%SRC_DIR%data" (
    xcopy /s /y /q "%SRC_DIR%data" "%BIN_DIR%\data\"
    echo 数据文件已复制到 bin\data\
)

:: 复制PROJ数据文件到bin目录
echo 复制PROJ数据到bin目录...
xcopy /s /y /q "C:\dev\vcpkg\installed\x64-windows\share\proj" "%BIN_DIR%\share\proj\"
echo PROJ数据已复制到 bin\share\proj\

:: 复制头文件（用于开发）
echo 复制头文件...
xcopy /s /y /q "%BUILD_DIR%\build_include\rocky" "%REDIST_DIR%\include\rocky\"
echo 头文件已复制到 include\rocky\

:: 创建启动脚本
echo 创建启动脚本...
(
echo @echo off
echo :: Rocky Demo 启动脚本
echo set ROCKY_FILE_PATH=%%~dp0
echo set VSG_FILE_PATH=%%~dp0
echo set ROCKY_DEFAULT_FONT=%%~dp0fonts\calibri.ttf
echo.
echo echo 启动Rocky Demo...
echo echo 工作目录: %%CD%%
echo echo ROCKY_FILE_PATH=%%ROCKY_FILE_PATH%%
echo echo VSG_FILE_PATH=%%VSG_FILE_PATH%%
echo echo ROCKY_DEFAULT_FONT=%%ROCKY_DEFAULT_FONT%%
echo echo.
echo.
echo .\rocky_demo.exe --log-level info
echo pause
) > "%BIN_DIR%\start_rocky_demo.bat"

echo 启动脚本已创建: bin\start_rocky_demo.bat

echo.
echo 编译和部署完成！
echo.
echo 目录结构:
echo %REDIST_DIR%\
echo   ├── bin\           - 可执行文件和运行时依赖
echo   │   ├── fonts\     - 字体文件
echo   │   ├── shaders\   - 着色器文件
echo   │   ├── data\      - 数据文件
echo   │   └── share\     - 共享文件
echo   ├── include\       - 头文件（开发用）
echo   └── lib\           - 库文件（开发用）
echo.
echo 使用方式:
echo 1. 运行: %BIN_DIR%\start_rocky_demo.bat
echo 2. 或直接在bin目录下运行: rocky_demo.exe
echo.
pause 